'use client';

import { useState } from 'react';
import Image from 'next/image';

const ExperienceSection = () => {
  const [expandedItems, setExpandedItems] = useState<number[]>([]);

  const toggleExpanded = (index: number) => {
    setExpandedItems(prev =>
      prev.includes(index)
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const experiences = [
    {
      id: 1,
      position: 'Co-Founder',
      company: 'Life Coach Elevate',
      duration: '2024 - Present',
      location: 'Arizona, USA',
      website: 'https://lifecoachelevate.com',
      logo: '/assets/companies/life-coach-elevate.png',
      description: 'Leading the technical development and DevOps infrastructure for a life coaching platform. Implementing scalable solutions using modern cloud technologies and CI/CD pipelines.',
      technologies: ['DevOps', 'CI/CD', 'Kubernetes', 'JavaScript', 'TypeScript', 'NextJS']
    },
    {
      id: 2,
      position: 'Senior Lead Software Engineer',
      company: 'Saimon Global Ltd',
      duration: '2019 - 2024',
      location: 'Dhaka, Bangladesh',
      website: 'https://saimonglobal.com',
      logo: '/assets/companies/saimon-global.png',
      description: 'Led a team of developers in building scalable web and mobile applications. Specialized in React, NextJS, and Flutter development for enterprise clients worldwide.',
      technologies: ['JavaScript', 'TypeScript', 'Dart', 'React', 'NextJS', 'Flutter']
    },
    {
      id: 3,
      position: 'Web Developer',
      company: 'influenceTHIS Canada',
      duration: '2018-2019',
      location: 'Remote (Toronto, Canada)',
      website: 'https://influencethis.ca',
      logo: '/assets/companies/influence-this.png',
      description: 'Developed responsive web applications and marketing websites for Canadian businesses. Focused on performance optimization and modern web technologies.',
      technologies: ['JavaScript', 'GULP', 'SCSS', 'Node.js']
    },
    {
      id: 4,
      position: 'Top Rated Web Developer',
      company: 'Upwork Inc.',
      duration: '2017 - Present',
      location: 'Remote',
      website: 'https://upwork.com',
      logo: '/assets/companies/upwork.png',
      description: 'Maintained a 100% success rate while working with 50+ clients worldwide. Delivered 140+ projects ranging from simple websites to complex web applications.',
      technologies: ['JavaScript', 'PHP', 'HTML', 'CSS', 'Figma']
    }
  ];

  return (
    <section id="experience" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Professional <span className="text-blue-600">Experience</span>
          </h2>
          <div className="w-24 h-1 bg-blue-600 mx-auto"></div>
        </div>

        {/* Experience Cards */}
        <div className="space-y-6 max-w-4xl mx-auto">
          {experiences.map((exp, index) => (
            <div
              key={exp.id}
              className="bg-purple-600 rounded-lg p-6 hover:bg-purple-700 transition-all duration-300"
            >
              <div className="flex items-start gap-4">
                {/* Company Logo */}
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-white/10 rounded-lg overflow-hidden">
                    <Image
                      src={exp.logo}
                      alt={`${exp.company} logo`}
                      width={48}
                      height={48}
                      className="object-cover w-full h-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                  </div>
                </div>

                {/* Experience Details */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="text-white font-semibold text-lg mb-1">
                        {exp.position} @ {exp.company}
                      </h3>
                      <p className="text-white/90 text-sm mb-2">{exp.duration}</p>
                      <div className="flex items-center gap-4 text-sm text-white/70 mb-4">
                        <div className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                          <span>{exp.location}</span>
                        </div>
                        <a
                          href={exp.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-white/70 hover:text-white transition-colors"
                        >
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                          </svg>
                        </a>
                      </div>
                    </div>
                    {/* Expand/Collapse Button */}
                    <button
                      onClick={() => toggleExpanded(index)}
                      className="text-white/70 hover:text-white transition-colors"
                    >
                      <span className="text-xl">
                        {expandedItems.includes(index) ? '−' : '+'}
                      </span>
                    </button>
                  </div>

                  {/* Description - Collapsible */}
                  {expandedItems.includes(index) && (
                    <div className="mb-4">
                      <p className="text-white/80 mb-4 leading-relaxed text-sm">
                        {exp.description}
                      </p>
                    </div>
                  )}

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2">
                    {exp.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-3 py-1 bg-white/10 text-white rounded-full text-xs font-medium"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ExperienceSection;
