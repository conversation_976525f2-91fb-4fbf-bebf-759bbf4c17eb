'use client';

import React, { useRef, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { FaExternalLinkAlt, FaGithub, FaArrowRight } from 'react-icons/fa';
import { usePortfolioStore } from '@/store/usePortfolioStore';
import Image from 'next/image';

const WorkSection = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, margin: '-100px' });
  const { projects } = usePortfolioStore();
  
  const [activeFilter, setActiveFilter] = useState('All');
  
  // Get unique categories
  const categories = ['All', ...Array.from(new Set(projects.map(project => project.category)))];
  
  // Filter projects based on active filter
  const filteredProjects = activeFilter === 'All' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  // Get featured project
  const featuredProject = projects.find(project => project.featured);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="work" ref={sectionRef} className="py-20 relative">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section Header with Reference Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Side - Title and Description */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              <span className="text-foreground">My</span><br />
              <span className="text-primary">Work</span>
            </h2>
            <p className="text-muted-foreground text-lg leading-relaxed mb-6">
              Deployed scalable travel, event and telemedicine web and hybrid mobile apps using React SPA and PWA.<br />
              Collaborated in 140+ projects with 50+ clients all around the world. I am also interested in data analytics and visualization.
            </p>
          </motion.div>

          {/* Right Side - Code Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative"
          >
            <div className="aspect-video bg-gradient-to-br from-primary/10 to-accent/10 rounded-lg overflow-hidden">
              <div className="w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDQwMCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjQwIiBmaWxsPSIjMUExQTFBIi8+Cjx0ZXh0IHg9IjIwIiB5PSI0MCIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxNCIgZmlsbD0iI0ZGNTcyMiI+Jmx0O2h0bWwmZ3Q7PC90ZXh0Pgo8dGV4dCB4PSI0MCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJtb25vc3BhY2UiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiNGRkZGRkYiPiZsdDtoZWFkJmd0OzwvdGV4dD4KPHR5eHQgeD0iNjAiIHk9IjgwIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0IiBmaWxsPSIjNjFEQUZCIj4mbHQ7dGl0bGUmZ3Q7SGVsbG8gV29ybGQmbHQ7L3RpdGxlJmd0OzwvdGV4dD4KPHR5eHQgeD0iNDAiIHk9IjEwMCIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxNCIgZmlsbD0iI0ZGRkZGRiI+Jmx0Oy9oZWFkJmd0OzwvdGV4dD4KPHR5eHQgeD0iNDAiIHk9IjEyMCIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxNCIgZmlsbD0iI0ZGRkZGRiI+Jmx0O2JvZHkmZ3Q7PC90ZXh0Pgo8dGV4dCB4PSI2MCIgeT0iMTQwIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0IiBmaWxsPSIjMTBCOTgxIj4mbHQ7aDEmZ3Q7SGVsbG8sIFdvcmxkISZsdDsvaDE+Z3Q7PC90ZXh0Pgo8dGV4dCB4PSI0MCIgeT0iMTYwIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0IiBmaWxsPSIjRkZGRkZGIj4mbHQ7L2JvZHkmZ3Q7PC90ZXh0Pgo8dGV4dCB4PSIyMCIgeT0iMTgwIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0IiBmaWxsPSIjRkY1NzIyIj4mbHQ7L2h0bWwmZ3Q7PC90ZXh0Pgo8L3N2Zz4K')] bg-cover bg-center opacity-80"></div>
            </div>
          </motion.div>
        </div>

        {/* Featured Project - Reference Layout */}
        {featuredProject && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-20"
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Left Side - Project Info */}
              <div className="order-2 lg:order-1">
                <div className="flex items-center mb-6">
                  <div className="w-8 h-8 flex items-center justify-center mr-3">
                    <FaArrowRight className="text-primary" />
                  </div>
                  <h3 className="text-lg font-bold text-foreground">Featured Project</h3>
                </div>

                <h4 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                  {featuredProject.title}
                </h4>

                <motion.a
                  href={featuredProject.liveUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors font-medium"
                  whileHover={{ x: 5 }}
                >
                  View Project
                  <FaExternalLinkAlt className="w-4 h-4" />
                </motion.a>
              </div>

              {/* Right Side - Device Mockup */}
              <div className="order-1 lg:order-2 relative">
                <motion.div
                  className="relative max-w-md mx-auto"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                >
                  {/* Phone Frame */}
                  <div className="relative bg-gray-900 rounded-[2.5rem] p-2 shadow-2xl">
                    <div className="bg-black rounded-[2rem] overflow-hidden">
                      {/* Screen Content */}
                      <div className="aspect-[9/19.5] bg-white relative">
                        <Image
                          src={featuredProject.image}
                          alt={featuredProject.title}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                        {/* Phone UI Elements */}
                        <div className="absolute top-0 left-0 right-0 h-8 bg-black/10 flex items-center justify-center">
                          <div className="w-16 h-1 bg-black/20 rounded-full"></div>
                        </div>
                      </div>
                    </div>
                    {/* Phone Button */}
                    <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gray-800 rounded-full border-2 border-gray-700"></div>
                  </div>

                  {/* Floating Elements */}
                  <motion.div
                    className="absolute -top-4 -right-4 w-8 h-8 bg-purple-500 rounded-lg"
                    animate={{
                      y: [0, -10, 0],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <motion.div
                    className="absolute -bottom-4 -left-4 w-6 h-6 bg-blue-500 rounded-full"
                    animate={{
                      y: [0, 10, 0],
                      scale: [1, 1.2, 1],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                  />
                </motion.div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Filter Buttons - Reference Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mb-12"
        >
          <div className="flex flex-wrap items-center gap-2 text-sm">
            <span className="text-muted-foreground mr-2">Filter by</span>
            {categories.map((category, index) => (
              <React.Fragment key={category}>
                <motion.button
                  onClick={() => setActiveFilter(category)}
                  className={`transition-all duration-300 ${
                    activeFilter === category
                      ? 'text-primary font-medium'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {category} {category === 'All' ? projects.length.toString().padStart(2, '0') : projects.filter(p => p.category === category).length.toString().padStart(2, '0')}
                </motion.button>
                {index < categories.length - 1 && (
                  <span className="text-muted-foreground">/</span>
                )}
              </React.Fragment>
            ))}
          </div>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredProjects.filter(project => !project.featured).map((project, index) => (
            <motion.div
              key={project.id}
              variants={itemVariants}
              layout
              className="group"
            >
              <motion.div
                className="glass rounded-xl overflow-hidden border border-border hover:border-primary/50 transition-all duration-300"
                whileHover={{ y: -10 }}
              >
                {/* Project Image */}
                <div className="aspect-video bg-secondary relative overflow-hidden">
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <div className="flex gap-4">
                      {project.liveUrl && (
                        <motion.a
                          href={project.liveUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-12 h-12 bg-white rounded-full flex items-center justify-center text-black hover:bg-primary hover:text-white transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <FaExternalLinkAlt className="w-4 h-4" />
                        </motion.a>
                      )}
                      {project.githubUrl && (
                        <motion.a
                          href={project.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-12 h-12 bg-white rounded-full flex items-center justify-center text-black hover:bg-primary hover:text-white transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <FaGithub className="w-4 h-4" />
                        </motion.a>
                      )}
                    </div>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                      {project.title}
                    </h3>
                    <span className="text-xs text-primary bg-primary/10 px-2 py-1 rounded">
                      {project.category}
                    </span>
                  </div>
                  
                  <p className="text-muted-foreground mb-4 text-sm">
                    {project.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.slice(0, 3).map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="text-xs text-muted-foreground bg-secondary px-2 py-1 rounded"
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span className="text-xs text-muted-foreground">
                        +{project.technologies.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default WorkSection;
