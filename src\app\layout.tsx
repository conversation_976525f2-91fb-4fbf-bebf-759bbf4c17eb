import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON> - Software Engineer, Front-end & App Developer",
  description: "Software Engineer specializing in React, NextJS, Flutter development. Top-rated developer with 140+ projects and 50+ clients worldwide.",
  keywords: "<PERSON><PERSON> Sen, Software Engineer, Frontend Developer, React, NextJS, Flutter, JavaScript, TypeScript",
  authors: [{ name: "<PERSON><PERSON> Sen" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      </head>
      <body className={`${inter.variable} font-inter antialiased bg-white text-gray-900`}>
        {/* Skip to content link for accessibility */}
        <a
          href="#main"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50"
        >
          Skip to content
        </a>
        {children}
      </body>
    </html>
  );
}
