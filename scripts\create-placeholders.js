// Script to create placeholder files for assets
const fs = require('fs');
const path = require('path');

// Create directories
const dirs = [
  'public/assets/publications',
  'public/assets/projects',
  'public/assets/companies',
  'public/assets/icons',
  'public/assets/testimonials'
];

dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Create placeholder files
const placeholders = [
  // Publications
  'public/assets/publications/wearedevelopers.png',
  'public/assets/publications/colorlib.png',
  'public/assets/publications/masai.png',
  'public/assets/publications/blogduwebdesign.png',
  'public/assets/publications/featured.png',
  'public/assets/publications/hostinger.png',
  'public/assets/publications/upwork.png',
  'public/assets/publications/careerfoundry.png',
  'public/assets/publications/frontendmentor.png',
  
  // Projects
  'public/assets/projects/flight-local.jpg',
  'public/assets/projects/ai-lab-granada.jpg',
  'public/assets/projects/tryotel-app.jpg',
  'public/assets/projects/khora.jpg',
  'public/assets/projects/tapy.jpg',
  'public/assets/projects/walker-ip.jpg',
  'public/assets/projects/tryotel-web.jpg',
  'public/assets/projects/kananaskis-spa.jpg',
  'public/assets/projects/higher-thought.jpg',
  'public/assets/projects/chittagong-roads.jpg',
  
  // Companies
  'public/assets/companies/life-coach-elevate.png',
  'public/assets/companies/saimon-global.png',
  'public/assets/companies/influence-this.png',
  'public/assets/companies/upwork.png',
  
  // Icons
  'public/assets/icons/code-icon.svg',
  'public/assets/icons/react-icon.svg',
  'public/assets/icons/flutter-icon.svg',
  
  // Testimonials
  'public/assets/testimonials/mark-greenspan.jpg',
  'public/assets/testimonials/wilfried-hajek.jpg',
  'public/assets/testimonials/jonathan-castro.jpg'
];

placeholders.forEach(file => {
  const content = `# Placeholder for ${path.basename(file)}`;
  fs.writeFileSync(file, content);
  console.log(`Created placeholder: ${file}`);
});

console.log('All placeholder files created successfully!');
