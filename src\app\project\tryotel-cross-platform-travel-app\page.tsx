import Image from 'next/image';
import Link from 'next/link';

export default function TryotelProjectPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-blue-600">Home</Link>
            <span>/</span>
            <Link href="/#work" className="hover:text-blue-600">Portfolio</Link>
            <span>/</span>
            <span className="text-gray-900">Tryotel – Cross-Platform Travel App</span>
          </div>
        </div>
      </nav>

      {/* Project Content */}
      <main className="max-w-4xl mx-auto px-4 py-12">
        {/* Project Header */}
        <div className="mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Tryotel – Cross-Platform Travel App
          </h1>
          
          {/* Project Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Strategy</h3>
              <p className="text-gray-600">CLEAN architecture, BLoC pattern</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Client</h3>
              <p className="text-gray-600">Saimon Global</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Technology</h3>
              <p className="text-gray-600">Flutter</p>
            </div>
          </div>

          {/* Project Description */}
          <div className="prose max-w-none mb-8">
            <p className="text-lg text-gray-700 leading-relaxed">
              Tryotel is a comprehensive cross-platform travel application built with Flutter, 
              designed to provide seamless booking experiences for travelers. The app implements 
              CLEAN architecture principles and BLoC pattern for maintainable and scalable code.
            </p>
          </div>

          {/* Open Project Button */}
          <a
            href="https://tryo.tel/app"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            Open Project
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </a>
        </div>

        {/* Project Image */}
        <div className="mb-12">
          <Image
            src="/assets/projects/tryotel-app.jpg"
            alt="Tryotel Cross-Platform Travel App"
            width={800}
            height={600}
            className="w-full rounded-lg shadow-lg"
          />
        </div>

        {/* Social Sharing */}
        <div className="mb-12">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Share this project</h3>
          <div className="flex space-x-4">
            <a
              href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-700"
            >
              Facebook
            </a>
            <a
              href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=Check out this amazing project`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-500"
            >
              Twitter
            </a>
            <a
              href={`https://pinterest.com/pin/create/button/?url=${encodeURIComponent(window.location.href)}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-red-600 hover:text-red-700"
            >
              Pinterest
            </a>
          </div>
        </div>

        {/* Next Projects */}
        <div className="border-t border-gray-200 pt-12">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Next Projects</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Link href="/project/flight-local" className="group">
              <div className="bg-gray-50 rounded-lg p-4 group-hover:bg-gray-100 transition-colors">
                <h4 className="font-medium text-gray-900 group-hover:text-blue-600">Flight Local</h4>
                <p className="text-sm text-gray-600">B2B Travel Solution</p>
              </div>
            </Link>
            <Link href="/project/ai-lab-granada" className="group">
              <div className="bg-gray-50 rounded-lg p-4 group-hover:bg-gray-100 transition-colors">
                <h4 className="font-medium text-gray-900 group-hover:text-blue-600">AI Lab Granada</h4>
                <p className="text-sm text-gray-600">AI Research Platform</p>
              </div>
            </Link>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 py-8 mt-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <p className="text-sm text-gray-500">
            © 2021. Made with passion by{' '}
            <a href="/" className="text-blue-600 hover:text-blue-700">
              Tamal Sen
            </a>
            . All right reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
