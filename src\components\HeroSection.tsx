'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const publications = [
    { name: 'WeAreDevelopers', logo: '/assets/publications/wearedevelopers.png', url: 'https://www.wearedevelopers.com/magazine/web-developer-portfolio-examples' },
    { name: 'Colorlib', logo: '/assets/publications/colorlib.png', url: 'https://colorlib.com/wp/developer-portfolios/' },
    { name: 'Masai School', logo: '/assets/publications/masai.png', url: 'https://www.masaischool.com/blog/10-web-developer-portfolio-examples-to-get-inspired/' },
    { name: 'Blog du Webdesign', logo: '/assets/publications/blogduwebdesign.png', url: 'https://www.blogduwebdesign.com/blog/graphisme/inspiration-graphique/portfolios-developpeurs.html' },
    { name: 'Featured', logo: '/assets/publications/featured.png', url: 'https://blog.featured.com/what-should-be-in-a-portfolio-for-web-developer/' },
    { name: '<PERSON><PERSON>', logo: '/assets/publications/hostinger.png', url: 'https://www.hostinger.com/tutorials/web-developer-portfolio' },
    { name: 'Upwork', logo: '/assets/publications/upwork.png', url: 'https://www.upwork.com/resources/web-developer-portfolio-examples' },
    { name: 'CareerFoundry', logo: '/assets/publications/careerfoundry.png', url: 'https://careerfoundry.com/en/blog/web-development/software-engineer-portfolio/' },
    { name: 'Frontend Mentor', logo: '/assets/publications/frontendmentor.png', url: 'https://www.frontendmentor.io/articles/building-an-effective-frontend-developer-portfolio--7cE8BfMG_' }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % Math.ceil(publications.length / 3));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + Math.ceil(publications.length / 3)) % Math.ceil(publications.length / 3));
  };

  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    const targetId = href.replace('#', '');
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };



  return (
    <section id="hero-section" className="min-h-screen flex items-center justify-center bg-white pt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Main Title */}
        <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-gray-900 mb-6 tracking-tight">
          Tamal Sen
        </h1>

        {/* Subtitle */}
        <h2 className="text-xl md:text-2xl text-gray-600 mb-16 font-light">
          Software Engineer, Front end & App Developer.
        </h2>

        {/* Featured Publications Section */}
        <div className="mb-16">
          <p className="text-sm text-gray-500 mb-8 uppercase tracking-wider">As featured in</p>

          {/* Publications Carousel */}
          <div className="relative max-w-4xl mx-auto">
            <div className="overflow-hidden">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
              >
                {Array.from({ length: Math.ceil(publications.length / 3) }).map((_, slideIndex) => (
                  <div key={slideIndex} className="w-full flex-shrink-0">
                    <div className="flex justify-center items-center space-x-8 md:space-x-12">
                      {publications.slice(slideIndex * 3, (slideIndex + 1) * 3).map((publication, index) => (
                        <a
                          key={index}
                          href={publication.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="opacity-60 hover:opacity-100 transition-opacity duration-300"
                        >
                          <Image
                            src={publication.logo}
                            alt={publication.name}
                            width={120}
                            height={40}
                            className="h-8 w-auto object-contain filter grayscale hover:grayscale-0 transition-all duration-300"
                          />
                        </a>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Buttons */}
            <button
              onClick={prevSlide}
              className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-300"
              aria-label="Previous slide"
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button
              onClick={nextSlide}
              className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-300"
              aria-label="Next slide"
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Scroll Down Arrow */}
        <div className="mt-16">
          <a
            href="#expertise"
            onClick={(e) => handleSmoothScroll(e, '#expertise')}
            className="inline-block animate-bounce"
          >
            <Image
              src="/assets/scroll-down-arrow.svg"
              alt="Scroll down"
              width={24}
              height={24}
              className="opacity-60 hover:opacity-100 transition-opacity duration-300"
            />
          </a>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
