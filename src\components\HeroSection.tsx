'use client';

import { useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import Image from 'next/image';
import { usePortfolioStore } from '@/store/usePortfolioStore';
import { FaChevronDown } from 'react-icons/fa';
import dynamic from 'next/dynamic';

// Import the 3D model component with dynamic loading to avoid SSR issues
const HeroModel = dynamic(() => import('./HeroModel'), {
  ssr: false,
  loading: () => <div className="w-full h-full flex items-center justify-center"><div className="w-20 h-20 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div></div>
});

const HeroSection = () => {
  const { name, role, featuredIn } = usePortfolioStore();
  const controls = useAnimation();
  const ref = useRef(null);
  const inView = useInView(ref, { once: false });

  // Split the name to highlight the last name
  const nameParts = name.split(' ');
  const firstName = nameParts[0];
  const lastName = nameParts.slice(1).join(' ');

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  // Enhanced text animation variants
  const textVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.9 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        delay: i * 0.15,
        duration: 0.8,
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    })
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  // Enhanced floating animation for the profile container
  const floatingAnimation = {
    y: [0, -20, 0],
    rotate: [0, 2, 0],
    transition: {
      duration: 8,
      repeat: Infinity,
      repeatType: 'reverse' as const,
      ease: "easeInOut"
    }
  };

  // Stagger animation for background elements
  const backgroundVariants = {
    hidden: { opacity: 0, scale: 0 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const backgroundItemVariants = {
    hidden: { opacity: 0, scale: 0, rotate: 0 },
    visible: {
      opacity: [0.6, 0.8, 0.6],
      scale: 1,
      rotate: 360,
      transition: {
        duration: 2,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="hero-section" ref={ref} className="min-h-screen flex items-center pt-24 pb-16 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-background"></div>

      {/* 3D Geometric Elements */}
      <motion.div
        className="absolute top-32 right-32 w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg shadow-2xl"
        animate={{
          y: [0, -20, 0],
          rotate: [0, 15, 0],
          rotateY: [0, 180, 360],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        style={{
          transformStyle: 'preserve-3d',
        }}
      />

      <motion.div
        className="absolute top-48 right-48 w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg shadow-xl"
        animate={{
          y: [0, -15, 0],
          rotate: [0, -20, 0],
          rotateX: [0, 180, 360],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
        style={{
          transformStyle: 'preserve-3d',
        }}
      />

      <motion.div
        className="absolute top-64 right-64 w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full shadow-lg"
        animate={{
          y: [0, -10, 0],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      />

      <motion.div
        className="absolute bottom-40 left-20 w-14 h-14 bg-gradient-to-br from-green-400 to-green-600 rounded-lg shadow-xl"
        animate={{
          y: [0, -25, 0],
          rotate: [-12, -25, -12],
          rotateZ: [0, 360],
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5
        }}
        style={{
          transformStyle: 'preserve-3d',
        }}
      />

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center">
          {/* Name and Title */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="mb-8"
          >
            <motion.h1
              variants={itemVariants}
              className="text-6xl md:text-8xl lg:text-9xl font-bold mb-6 tracking-wider"
            >
              <span className="text-white">TAMAL SEN</span>
            </motion.h1>

            <motion.h2
              variants={itemVariants}
              className="text-lg md:text-xl text-gray-300 mb-12 tracking-widest font-mono uppercase"
            >
              SOFTWARE ENGINEER, FRONT END & APP DEVELOPER.
            </motion.h2>
          </motion.div>

          {/* Featured In Section */}
          <motion.div
            variants={itemVariants}
            className="mb-16"
          >
            <p className="text-sm text-muted-foreground mb-6">As featured in</p>

            {/* Publications Carousel */}
            <div className="relative overflow-hidden">
              <motion.div
                className="flex items-center justify-center gap-12"
                animate={{ x: [0, -100, 0] }}
                transition={{
                  duration: 20,
                  repeat: Infinity,
                  ease: "linear"
                }}
              >
                {/* Duplicate the array for seamless loop */}
                {[...featuredIn, ...featuredIn].map((publication, index) => (
                  <motion.div
                    key={`${publication}-${index}`}
                    className="flex-shrink-0 opacity-40 hover:opacity-70 transition-opacity cursor-pointer"
                    whileHover={{ scale: 1.05 }}
                  >
                    <div className="w-32 h-8 bg-muted-foreground/20 rounded flex items-center justify-center">
                      <span className="text-xs font-medium text-muted-foreground">
                        {publication}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </div>

            {/* Navigation dots */}
            <div className="flex justify-center gap-2 mt-6">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <div className="w-2 h-2 bg-muted-foreground/30 rounded-full"></div>
              <div className="w-2 h-2 bg-muted-foreground/30 rounded-full"></div>
            </div>
          </motion.div>

          {/* Scroll Down Indicator */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.5 }}
            className="flex flex-col items-center"
          >
            <motion.a
              href="#expertise"
              className="flex flex-col items-center text-muted-foreground hover:text-primary transition-colors group"
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <FaChevronDown className="w-6 h-6 group-hover:scale-110 transition-transform" />
            </motion.a>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
