'use client';

import Image from 'next/image';

const ContactSection = () => {
  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      position: 'Founder at influenceTHIS Canada',
      company: 'influenceTHIS Canada',
      avatar: '/assets/testimonials/mark-greenspan.jpg',
      testimonial: '<PERSON><PERSON> is an exceptional developer who consistently delivers high-quality work. His attention to detail and technical expertise made our project a huge success.'
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON>',
      position: 'Agile Coach | Speaker | Trainer',
      company: 'Independent',
      avatar: '/assets/testimonials/wilfried-hajek.jpg',
      testimonial: 'Working with <PERSON><PERSON> was a pleasure. His professionalism and ability to understand complex requirements quickly made him an invaluable team member.'
    },
    {
      id: 3,
      name: '<PERSON>',
      position: 'CEO & Founder at The Cliff',
      company: 'The Cliff',
      avatar: '/assets/testimonials/jonathan-castro.jpg',
      testimonial: '<PERSON><PERSON> delivered beyond our expectations. His technical skills combined with excellent communication made our collaboration seamless and productive.'
    }
  ];
  return (
    <section id="contact" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Testimonials Section */}
        <div className="mb-20">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial) => (
              <div
                key={testimonial.id}
                className="bg-gray-50 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300"
              >
                <div className="flex items-center mb-4">
                  <Image
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    width={48}
                    height={48}
                    className="rounded-full mr-4"
                  />
                  <div>
                    <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                    <p className="text-sm text-gray-600">{testimonial.position}</p>
                    <p className="text-xs text-gray-500">{testimonial.company}</p>
                  </div>
                </div>
                <p className="text-gray-700 text-sm leading-relaxed italic">
                  "{testimonial.testimonial}"
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Section */}
        <div className="text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
            Available for select freelance opportunities
          </h2>

          <p className="text-lg text-gray-600 mb-12 leading-relaxed max-w-2xl mx-auto">
            Have an exciting project you need help with?<br />
            Send me an email or contact me via instant message!
          </p>

          {/* Email */}
          <div className="mb-12">
            <a
              href="mailto:<EMAIL>"
              className="text-3xl md:text-4xl font-bold text-gray-900 hover:text-blue-600 transition-colors"
            >
              <EMAIL>
            </a>
          </div>

          {/* Social Links */}
          <div className="flex flex-wrap justify-center gap-8 text-gray-600">
            <a
              href="https://m.me/tamalsen"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 transition-colors underline"
            >
              Messenger
            </a>

            <a
              href="https://www.linkedin.com/in/tamalsen"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 transition-colors underline"
            >
              LinkedIn
            </a>

            <a
              href="https://www.instagram.com/tamalsen"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 transition-colors underline"
            >
              Instagram
            </a>

            <a
              href="https://github.com/tamalsen"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-blue-600 transition-colors underline"
            >
              Github
            </a>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-20 pt-8 border-t border-gray-200 text-center">
          <p className="text-sm text-gray-500">
            © 2021. Made with passion by{' '}
            <a href="#" className="text-blue-600 hover:text-blue-700">
              Tamal Sen
            </a>
            . All right reserved.
          </p>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
