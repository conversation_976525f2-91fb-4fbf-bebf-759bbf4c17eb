'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { FaCode, FaReact, FaMobile } from 'react-icons/fa';
import { usePortfolioStore } from '@/store/usePortfolioStore';

const ExpertiseSection = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, margin: '-100px' });
  const { expertise } = usePortfolioStore();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'code':
        return <FaCode className="w-8 h-8" />;
      case 'react':
        return <FaReact className="w-8 h-8" />;
      case 'mobile':
        return <FaMobile className="w-8 h-8" />;
      default:
        return <FaCode className="w-8 h-8" />;
    }
  };

  return (
    <section id="expertise" ref={sectionRef} className="py-20 relative">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="text-foreground">My </span>
            <span className="text-primary">Expertise</span>
          </h2>
          <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
        </motion.div>

        {/* Expertise Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {expertise.map((item, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group"
            >
              <motion.div
                className="bg-gray-800/50 border border-gray-700 rounded-lg p-8 hover:border-gray-600 transition-all duration-300 h-full"
                whileHover={{
                  y: -5,
                  transition: { duration: 0.3 }
                }}
              >
                {/* Icon */}
                <motion.div
                  className="w-12 h-12 rounded-lg bg-gray-700/50 flex items-center justify-center mb-6 text-white"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.3 }}
                >
                  {getIcon(item.icon)}
                </motion.div>

                {/* Content */}
                <h3 className="text-lg font-semibold text-white mb-4">
                  {item.title}
                </h3>

                <p className="text-gray-300 text-sm leading-relaxed">
                  {item.description}
                </p>
              </motion.div>
            </motion.div>
          ))}
        </motion.div>

        {/* Quote Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-20"
        >
          <blockquote className="text-xl md:text-2xl font-light text-muted-foreground italic max-w-3xl mx-auto">
            "Sometimes the best way to solve a problem is to help others."
          </blockquote>
          <cite className="text-sm text-muted-foreground mt-4 block">
            - Uncle Iroh, 'Avatar: The Last Airbender'
          </cite>
        </motion.div>
      </div>
    </section>
  );
};

export default ExpertiseSection;
