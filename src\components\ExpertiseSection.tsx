'use client';

import Image from 'next/image';

const ExpertiseSection = () => {
  const expertiseAreas = [
    {
      title: 'Software Development',
      description: 'Experienced in both functional and OOP: Dart, Python, Java, JavaScript, TypeScript.',
      icon: '/assets/icons/code-icon.svg',
      technologies: ['Dart', 'Python', 'Java', 'JavaScript', 'TypeScript']
    },
    {
      title: 'Frontend Dev React, NextJS',
      description: 'Passionate about UI/UX. Over 5 years of development experience in HTML, CSS, JS, React and NextJS frameworks.',
      icon: '/assets/icons/react-icon.svg',
      technologies: ['HTML', 'CSS', 'JavaScript', 'React', 'NextJS']
    },
    {
      title: 'Flutter Dev Android, iOS',
      description: 'Skilled in developing hybrid mobile apps and cross-platform solutions using the Flutter framework.',
      icon: '/assets/icons/flutter-icon.svg',
      technologies: ['Flutter', 'Android', 'iOS', 'Cross-platform']
    }
  ];

  return (
    <section id="expertise" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            My <span className="text-blue-600">Expertise</span>
          </h2>
          <div className="w-24 h-1 bg-blue-600 mx-auto"></div>
        </div>

        {/* Expertise Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
          {expertiseAreas.map((area, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow duration-300 group"
            >
              {/* Icon */}
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-200 transition-colors duration-300">
                <Image
                  src={area.icon}
                  alt={area.title}
                  width={32}
                  height={32}
                  className="text-blue-600"
                />
              </div>

              {/* Content */}
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                {area.title}
              </h3>

              <p className="text-gray-600 text-sm leading-relaxed mb-6">
                {area.description}
              </p>

              {/* Technologies */}
              <div className="flex flex-wrap gap-2">
                {area.technologies.map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Quote Section */}
        <div className="text-center">
          <blockquote className="text-xl md:text-2xl font-light text-gray-600 italic max-w-3xl mx-auto">
            "Sometimes the best way to solve a problem is to help others."
          </blockquote>
          <cite className="text-sm text-gray-500 mt-4 block">
            - Uncle Iroh, 'Avatar: The Last Airbender'
          </cite>
        </div>
      </div>
    </section>
  );
};

export default ExpertiseSection;
